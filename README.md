# 知深学习导师（Zhi Deep Learning Mentor）

一个“左对话｜右内容”的双栏式深度学习应用，通过“动态上下文管理 + 可交互摘要”，帮助用户将外部信息转化为结构化知识。MVP 聚焦“粘贴文本 → 对话学习 → 自动保存/恢复”的闭环体验。

关联文档：[`docs/brief.md`](docs/brief.md)｜[`docs/prd.md`](docs/prd.md)｜[`docs/architecture.md`](docs/architecture.md)｜[`docs/ui-ux.md`](docs/ui-ux.md)｜Epic [`docs/epic-core-mvp.md`](docs/epic-core-mvp.md)

## 目标与范围（MVP）
- 目标：验证“动态上下文管理 + 可交互摘要”的价值；交付可运维、可演进的基线（统一错误模型、trace_id、流式/重试/超时、观测）。
- In Scope：纯文本粘贴建会话、双栏界面、动态摘要最小可用（摘要节点定位到对话）、自动保存与断点续学。
- Out of Scope：多格式导入、记忆熔炉/知识网络化、语音学习/播客、协作与分享（见 [`docs/prd.md`](docs/prd.md) 4.2）。

## 技术栈（MVP）
- 前端：React 18 + Vite 5（Zustand/Redux，Vitest+RTL）
- 后端：FastAPI（Uvicorn/Gunicorn，Pytest）
- 数据：Supabase（PostgreSQL/Auth/Storage）
- 观测：Sentry（前后端），结构化日志 + trace_id
- 部署：前端 Vercel/Netlify；后端 Render/Fly.io
- LLM：适配器层（OpenAI/Claude/Gemini 可插拔）

## 仓库结构（建议）
```
/phoenix-learning-app
├── apps/
│   ├── backend/          # FastAPI
│   └── frontend/         # React + Vite
├── docs/                 # 产品/架构/UX/规范与OpenAPI
└── packages/             # 共享类型/库
```

## 快速开始（本地）
前置：安装 Node 18+/pnpm 或 npm、Python 3.11+、Supabase 项目或等效 PG、Sentry DSN、LLM Key。

1) 克隆与依赖
```
git clone <repo-url>
cd <repo>
cp .env.example .env
```
- 填写 `.env`（详见“环境变量”一节）

2) 启动后端
```
cd apps/backend
pip install -r requirements.txt
uvicorn app.main:app --reload
```

3) 启动前端
```
cd apps/frontend
npm install
npm run dev
```

默认 API 前缀：`/api`（需要有效 JWT）。认证由 Supabase Auth 提供，后端中间件核验并注入用户上下文。

## API 概览
- POST `/api/sessions`：{ text } → { id, title, created_at }
- GET `/api/sessions/:id`：返回 messages、summary、reading_position
- POST `/api/sessions/:id/messages`：{ content } → 流式/最终响应（见 [`docs/openapi.yaml`](docs/openapi.yaml)）

统一错误模型与 trace_id：详见 [`docs/architecture.md`](docs/architecture.md) 7 与附录 12.1。

## 测试运行指南

### 前端测试
```bash
cd apps/frontend

# 运行所有测试
npm run test

# 运行测试并监听文件变化
npm run test:watch

# 运行测试覆盖率报告
npm run test:coverage

# 运行特定测试文件
npm run test Reader.test.tsx

# 调试模式运行测试
npm run test:debug
```

### 后端测试
```bash
cd apps/backend

# 运行所有测试
pytest

# 运行特定测试文件
pytest tests/test_sessions.py

# 运行测试并生成覆盖率报告
pytest --cov=app tests/

# 详细输出模式
pytest -v
```

### 测试策略指南
- **长时间等待测试 (>5s)**：使用真实时间 + DOM 条件等待
- **短时间精确控制 (<5s)**：使用 Fake Timers + 推进工具
- **复杂异步交互**：基于 DOM 状态变化进行断言
- **API 调用测试**：使用 Mock + 真实时间等待

详细测试策略请参考：[`docs/testing-strategy-guide.md`](docs/testing-strategy-guide.md)

### 测试稳定性标准
- 成功率目标：≥95% (连续10次运行)
- 执行时间限制：单个测试 ≤10秒
- CI 环境兼容性：本地与 CI 行为一致

## 环境变量（示例）
详见根目录 [`.env.example`](.env.example)。关键项：
- SUPABASE_URL / SUPABASE_ANON_KEY / SUPABASE_SERVICE_ROLE_KEY
- DATABASE_URL
- LLM_API_KEY / LLM_API_BASE
- SENTRY_DSN

## 贡献规范
- 分支策略：`main`（稳定生产）、`develop`（集成）、feature 分支：`feat/<scope>-<desc>`
- 提交信息：遵循 Conventional Commits（见 [`CONTRIBUTING.md`](CONTRIBUTING.md)）
- 代码风格：统一 EditorConfig（见 [`.editorconfig`](.editorconfig)），前端使用 ESLint/Prettier，后端使用 Ruff/Black（建议）
- 代码所有者：见 [`CODEOWNERS`](CODEOWNERS)

## CI/CD
- PR/Push develop：Lint/Format、单测、构建、预览部署
- Merge main：集成测试 + DB 迁移 + 生产部署
- Secrets：GitHub Encrypted Secrets（参考 docs“上线前准备清单”）

## 运维与观测
- Sentry 前后端接入；结构化 JSON 日志（含 trace_id）
- 关键指标：API 请求率/错误率/延迟（P50/90/99）、LLM token/延迟/错误率、成本预算告警
- 告警：API 错误率、P99 延迟、LLM 预算阈值（Slack）

## 路线图（摘要）
- 阶段A（MVP，0-6周）：FR1–FR9 闭环，观测/错误模型/重试与限流基线
- 阶段B（6-12周）：多格式导入、摘要↔对话↔原文三向回溯、间隔重复试验
- 阶段C（12周+）：知识网络化、语音学习、成本/可靠性优化与 A/B 实验

## 许可证
待定（MIT/Apache-2.0 建议）
