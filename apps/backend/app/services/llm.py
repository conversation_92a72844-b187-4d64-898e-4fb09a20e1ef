"""
LLM Service Adapter

Provides a unified interface for different LLM providers (OpenAI, Claude, Gemini, etc.)
with streaming support, error handling, and retry mechanisms.
"""

import asyncio
import json
import logging
from typing import AsyncGenerator, Dict, List, Optional, Any
from abc import ABC, abstractmethod
from dataclasses import dataclass
from enum import Enum

import httpx
from app.core.config import settings
from app.core.errors import AppError, ErrorCode


logger = logging.getLogger(__name__)


class LLMProvider(str, Enum):
    """Supported LLM providers."""
    OPENAI = "openai"
    CLAUDE = "claude"
    GEMINI = "gemini"


@dataclass
class LLMMessage:
    """Standardized message format for LLM interactions."""
    role: str  # "system", "user", "assistant"
    content: str
    metadata: Optional[Dict[str, Any]] = None


@dataclass
class LLMResponse:
    """Standardized response format from LLM."""
    content: str
    model: str
    usage: Optional[Dict[str, int]] = None
    metadata: Optional[Dict[str, Any]] = None


class LLMAdapter(ABC):
    """Abstract base class for LLM adapters."""
    
    def __init__(self, api_key: str, api_base: Optional[str] = None):
        self.api_key = api_key
        self.api_base = api_base
        self.client = httpx.AsyncClient(
            timeout=httpx.Timeout(settings.llm_request_timeout_ms / 1000.0)
        )
    
    @abstractmethod
    async def chat_completion(
        self,
        messages: List[LLMMessage],
        model: Optional[str] = None,
        stream: bool = False,
        **kwargs
    ) -> AsyncGenerator[str, None] | LLMResponse:
        """Generate chat completion."""
        pass
    
    @abstractmethod
    def _format_messages(self, messages: List[LLMMessage]) -> List[Dict[str, Any]]:
        """Format messages for the specific provider."""
        pass
    
    async def close(self):
        """Close the HTTP client."""
        await self.client.aclose()


class OpenAIAdapter(LLMAdapter):
    """OpenAI API adapter."""
    
    def __init__(self, api_key: str, api_base: Optional[str] = None):
        super().__init__(api_key, api_base)
        self.api_base = api_base or "https://api.openai.com/v1"
    
    def _format_messages(self, messages: List[LLMMessage]) -> List[Dict[str, Any]]:
        """Format messages for OpenAI API."""
        return [{"role": msg.role, "content": msg.content} for msg in messages]
    
    async def chat_completion(
        self,
        messages: List[LLMMessage],
        model: Optional[str] = None,
        stream: bool = False,
        **kwargs
    ) -> AsyncGenerator[str, None] | LLMResponse:
        """Generate chat completion using OpenAI API."""
        model = model or settings.llm_default_model
        formatted_messages = self._format_messages(messages)
        
        payload = {
            "model": model,
            "messages": formatted_messages,
            "stream": stream,
            **kwargs
        }
        
        headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }
        
        if stream:
            return self._stream_completion(payload, headers)
        else:
            return await self._single_completion(payload, headers, model)
    
    async def _single_completion(
        self, payload: Dict, headers: Dict, model: str
    ) -> LLMResponse:
        """Handle single (non-streaming) completion."""
        try:
            response = await self.client.post(
                f"{self.api_base}/chat/completions",
                json=payload,
                headers=headers
            )
            response.raise_for_status()
            
            data = response.json()
            content = data["choices"][0]["message"]["content"]
            usage = data.get("usage", {})
            
            return LLMResponse(
                content=content,
                model=model,
                usage=usage
            )
            
        except httpx.HTTPStatusError as e:
            logger.error(f"LLM API error: {e.response.status_code} - {e.response.text}")
            raise AppError(
                error_code=ErrorCode.EXTERNAL_SERVICE_ERROR,
                message=f"LLM API error: {e.response.status_code}",
                details={"provider": "openai", "status_code": e.response.status_code}
            )
        except Exception as e:
            logger.error(f"Unexpected LLM error: {str(e)}")
            raise AppError(
                error_code=ErrorCode.EXTERNAL_SERVICE_ERROR,
                message="LLM service unavailable",
                details={"provider": "openai", "error": str(e)}
            )
    
    async def _stream_completion(
        self, payload: Dict, headers: Dict
    ) -> AsyncGenerator[str, None]:
        """Handle streaming completion."""
        try:
            async with self.client.stream(
                "POST",
                f"{self.api_base}/chat/completions",
                json=payload,
                headers=headers
            ) as response:
                response.raise_for_status()
                
                async for line in response.aiter_lines():
                    if line.startswith("data: "):
                        data_str = line[6:]  # Remove "data: " prefix
                        
                        if data_str.strip() == "[DONE]":
                            break
                        
                        try:
                            data = json.loads(data_str)
                            if "choices" in data and len(data["choices"]) > 0:
                                delta = data["choices"][0].get("delta", {})
                                if "content" in delta:
                                    yield delta["content"]
                        except json.JSONDecodeError:
                            continue
                            
        except httpx.HTTPStatusError as e:
            logger.error(f"LLM streaming error: {e.response.status_code}")
            raise AppError(
                error_code=ErrorCode.EXTERNAL_SERVICE_ERROR,
                message=f"LLM streaming error: {e.response.status_code}",
                details={"provider": "openai", "status_code": e.response.status_code}
            )


class LLMService:
    """Main LLM service with provider abstraction and retry logic."""
    
    def __init__(self):
        self.adapter: Optional[LLMAdapter] = None
        self._initialize_adapter()
    
    def _initialize_adapter(self):
        """Initialize the appropriate LLM adapter based on configuration."""
        if not settings.llm_api_key:
            logger.warning("No LLM API key configured")
            return
        
        # For now, default to OpenAI adapter
        # TODO: Add provider detection logic based on API base or explicit config
        self.adapter = OpenAIAdapter(
            api_key=settings.llm_api_key,
            api_base=settings.llm_api_base
        )
        logger.info("LLM service initialized with OpenAI adapter")
    
    async def chat_completion(
        self,
        messages: List[LLMMessage],
        model: Optional[str] = None,
        stream: bool = False,
        **kwargs
    ) -> AsyncGenerator[str, None] | LLMResponse:
        """Generate chat completion with retry logic."""
        if not self.adapter:
            raise AppError(
                error_code=ErrorCode.SERVICE_UNAVAILABLE,
                message="LLM service not configured",
                details={"reason": "missing_api_key"}
            )
        
        for attempt in range(settings.external_max_retries + 1):
            try:
                return await self.adapter.chat_completion(
                    messages=messages,
                    model=model,
                    stream=stream,
                    **kwargs
                )
            except AppError as e:
                if attempt == settings.external_max_retries:
                    raise
                
                # Calculate exponential backoff delay
                delay = min(
                    settings.retry_backoff_initial_ms * (2 ** attempt) / 1000.0,
                    settings.retry_backoff_max_ms / 1000.0
                )
                
                logger.warning(
                    f"LLM request failed (attempt {attempt + 1}), retrying in {delay}s: {e.message}"
                )
                await asyncio.sleep(delay)
    
    async def close(self):
        """Close the LLM service and cleanup resources."""
        if self.adapter:
            await self.adapter.close()


# Global LLM service instance
llm_service = LLMService()
